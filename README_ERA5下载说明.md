# ERA5温度数据下载程序使用说明

## 程序功能
本程序用于从Copernicus Climate Data Store (CDS) 下载ERA5逐小时温度数据，支持所有压力层级。

## 文件说明
- `download_era5_temperature.py` - 主下载程序 (交互式配置)
- `download_era5_simple.py` - 简化下载程序 (固定配置)
- `install_requirements.py` - 依赖包安装程序
- `check_era5_data.py` - 数据检查和验证工具
- `run_era5_download.bat` - 多功能批处理文件
- `README_ERA5下载说明.md` - 本说明文档

## 使用方法

### 方法1: 批处理文件（推荐）
1. 双击运行 `run_era5_download.bat`
2. 选择需要的操作：
   - 选项1: 完整流程 (安装依赖 + 下载数据 + 检查数据)
   - 选项2: 仅安装依赖包
   - 选项3: 仅下载数据 (交互式配置)
   - 选项4: 仅下载数据 (简化版，固定配置)
   - 选项5: 仅检查已下载的数据

### 方法2: 手动运行
1. 安装依赖包：
   ```
   python install_requirements.py
   ```

2. 下载数据（选择其中一种）：
   ```
   python download_era5_temperature.py    # 交互式配置
   python download_era5_simple.py         # 简化版，固定配置
   ```

3. 检查数据：
   ```
   python check_era5_data.py
   ```

## 程序配置

### 账号信息
- 邮箱: <EMAIL>
- 密码: Zy.76801099

### 路径配置
- 激光雷达数据目录: `D:\lidar\supercooledwaterday-hourly`
- ERA5输出目录: `D:\lidar\supercooledwaterday-hourly\era5`

### 下载参数
- **变量**: Temperature (温度)
- **压力层级**: 所有37个层级 (1-1000 hPa)
- **时间分辨率**: 逐小时 (00:00-23:00)
- **数据格式**: NetCDF (.nc)
- **时间范围**: 自动从激光雷达文件名中提取日期

## 下载的压力层级
```
1, 2, 3, 5, 7, 10, 20, 30, 50, 70, 100, 125, 150, 175, 200, 
225, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 
775, 800, 825, 850, 875, 900, 925, 950, 975, 1000 hPa
```

## 程序工作流程

1. **检查环境**: 验证Python环境和依赖包
2. **提取日期**: 从 `D:\lidar\supercooledwaterday-hourly` 目录中的 `Depol_YYYYMMDD*.csv` 文件提取日期
3. **设置凭据**: 自动配置CDS API凭据文件 (`~/.cdsapirc`)
4. **下载数据**: 逐日下载ERA5温度数据
5. **保存文件**: 数据保存为 `era5_temperature_YYYYMMDD.nc` 格式

## 输出文件
- 文件位置: `D:\lidar\supercooledwaterday-hourly\era5\`
- 文件命名: `era5_temperature_YYYYMMDD.nc`
- 文件格式: NetCDF4
- 文件内容: 指定日期24小时的温度数据，包含所有压力层级

## 注意事项

1. **网络要求**: 需要稳定的网络连接，下载可能需要较长时间
2. **存储空间**: 每天的数据约50-100MB，请确保有足够存储空间
3. **CDS账号**: 确保CDS账号有效且有下载权限
4. **重复下载**: 程序会自动跳过已存在的文件
5. **错误处理**: 如果某天下载失败，程序会继续下载其他日期

## 依赖包
- `cdsapi` - Copernicus Climate Data Store API客户端
- `netcdf4` - NetCDF文件处理
- `xarray` - 科学数据处理
- `numpy` - 数值计算
- `pandas` - 数据处理

## 故障排除

### 常见问题
1. **CDS API错误**: 检查网络连接和账号凭据
2. **权限错误**: 确保对输出目录有写入权限
3. **依赖包错误**: 运行 `install_requirements.py` 重新安装依赖包

### 错误日志
程序会在控制台显示详细的下载进度和错误信息。

## 联系信息
如有问题，请检查：
1. CDS官方文档: https://cds.climate.copernicus.eu/api-how-to
2. ERA5数据集页面: https://cds.climate.copernicus.eu/datasets/reanalysis-era5-pressure-levels

## 程序特性

### 下载程序特性
- **自动日期提取**: 从激光雷达文件名自动提取需要下载的日期
- **断点续传**: 自动跳过已存在的文件，支持中断后继续下载
- **错误处理**: 单个文件下载失败不影响其他文件下载
- **进度显示**: 实时显示下载进度和文件大小
- **配置灵活**: 支持交互式配置和固定配置两种模式

### 数据检查特性
- **文件完整性检查**: 验证NetCDF文件是否可正常读取
- **数据内容检查**: 检查温度数据的维度、单位和数值范围
- **缺失日期检查**: 对比期望日期和已下载日期，找出缺失的数据
- **存储统计**: 显示文件大小和总存储占用

## 更新日志
- 2025-01-18: 初始版本
  - 支持ERA5温度数据下载
  - 添加交互式和简化版下载程序
  - 添加数据检查和验证工具
  - 添加多功能批处理界面
