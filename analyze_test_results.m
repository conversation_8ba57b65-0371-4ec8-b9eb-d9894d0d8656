clc; clear; close all;

%% 分析不同T和T2参数的测试结果
% 这个脚本用于分析lidarvsradar6pic_test_params_20250127.m生成的结果

%% 设置路径
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\backscatter\test_params';
date_str = '2025-01-27';  % 分析的日期

%% 定义测试的参数组合
test_params = [
    400, 8;    % T=400, T2=8
    600, 12;   % T=600, T2=12
    800, 16;   % T=800, T2=16
    1000, 20;  % T=1000, T2=20
    1200, 24;  % T=1200, T2=24
];

%% 初始化结果存储
results = struct();
results.params = test_params;
results.cloud_detected_times = zeros(size(test_params, 1), 1);
results.threshold_detections = zeros(size(test_params, 1), 1);
results.water_cloud_detections = zeros(size(test_params, 1), 1);
results.gradient_detections = zeros(size(test_params, 1), 1);
results.cloud_percentage = zeros(size(test_params, 1), 1);

%% 读取每个参数组合的结果
fprintf('分析不同T和T2参数的测试结果...\n');
fprintf('日期: %s\n', date_str);
fprintf('参数组合数量: %d\n\n', size(test_params, 1));

for i = 1:size(test_params, 1)
    F_threshold = test_params(i, 1);
    F2_threshold = test_params(i, 2);
    
    % 构建结果文件名
    result_filename = fullfile(outputDir, sprintf('%s_cloudmask_results_T%d_T2%d.mat', ...
        date_str, F_threshold, F2_threshold));
    
    if exist(result_filename, 'file')
        fprintf('读取参数组合 %d: T=%d, T2=%d\n', i, F_threshold, F2_threshold);
        
        % 加载结果
        data = load(result_filename);
        
        % 存储结果
        results.cloud_detected_times(i) = data.cloud_detected_times;
        results.threshold_detections(i) = data.threshold_detections;
        results.water_cloud_detections(i) = data.water_cloud_detections;
        results.gradient_detections(i) = data.gradient_detections;
        results.cloud_percentage(i) = data.cloud_percentage;
        
        fprintf('  云层检测事件数: %d\n', data.cloud_detected_times);
        fprintf('  云层覆盖率: %.1f%%\n', data.cloud_percentage);
        fprintf('  阈值检测: %d, 水云检测: %d, 梯度检测: %d\n\n', ...
            data.threshold_detections, data.water_cloud_detections, data.gradient_detections);
    else
        fprintf('警告: 找不到参数组合 %d 的结果文件: %s\n', i, result_filename);
    end
end

%% 创建对比分析图
fig = figure('Position', [100, 100, 1200, 800]);

% 子图1：云层检测事件数对比
subplot(2,3,1);
bar(1:size(test_params, 1), results.cloud_detected_times);
xlabel('参数组合');
ylabel('云层检测事件数');
title('不同参数下的云层检测事件数');
set(gca, 'XTick', 1:size(test_params, 1));
labels = cell(size(test_params, 1), 1);
for i = 1:size(test_params, 1)
    labels{i} = sprintf('T=%d\nT2=%d', test_params(i, 1), test_params(i, 2));
end
set(gca, 'XTickLabel', labels);
grid on;

% 子图2：云层覆盖率对比
subplot(2,3,2);
bar(1:size(test_params, 1), results.cloud_percentage);
xlabel('参数组合');
ylabel('云层覆盖率 (%)');
title('不同参数下的云层覆盖率');
set(gca, 'XTick', 1:size(test_params, 1));
set(gca, 'XTickLabel', labels);
grid on;

% 子图3：检测方法分布
subplot(2,3,3);
detection_data = [results.threshold_detections, results.water_cloud_detections, results.gradient_detections];
bar(1:size(test_params, 1), detection_data, 'stacked');
xlabel('参数组合');
ylabel('检测数量');
title('不同检测方法的贡献');
legend('阈值检测', '水云检测', '梯度检测', 'Location', 'best');
set(gca, 'XTick', 1:size(test_params, 1));
set(gca, 'XTickLabel', labels);
grid on;

% 子图4：T参数与云层检测的关系
subplot(2,3,4);
plot(test_params(:, 1), results.cloud_detected_times, 'o-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('T参数');
ylabel('云层检测事件数');
title('T参数与云层检测事件数的关系');
grid on;

% 子图5：T2参数与云层覆盖率的关系
subplot(2,3,5);
plot(test_params(:, 2), results.cloud_percentage, 's-', 'LineWidth', 2, 'MarkerSize', 8);
xlabel('T2参数');
ylabel('云层覆盖率 (%)');
title('T2参数与云层覆盖率的关系');
grid on;

% 子图6：参数敏感性分析
subplot(2,3,6);
% 计算相对于最小值的变化率
min_detections = min(results.cloud_detected_times);
relative_change = (results.cloud_detected_times - min_detections) / min_detections * 100;
bar(1:size(test_params, 1), relative_change);
xlabel('参数组合');
ylabel('相对变化率 (%)');
title('云层检测的参数敏感性');
set(gca, 'XTick', 1:size(test_params, 1));
set(gca, 'XTickLabel', labels);
grid on;

% 保存对比分析图
analysis_filename = fullfile(outputDir, sprintf('%s_parameter_analysis.png', date_str));
print(fig, analysis_filename, '-dpng', '-r300');
fprintf('保存参数分析图到: %s\n', analysis_filename);

%% 生成详细的统计报告
report_filename = fullfile(outputDir, sprintf('%s_parameter_analysis_report.txt', date_str));
fid = fopen(report_filename, 'w');

fprintf(fid, '=== 1月27日不同T和T2参数的CloudMask测试结果分析报告 ===\n\n');
fprintf(fid, '测试日期: %s\n', date_str);
fprintf(fid, '测试参数组合数量: %d\n\n', size(test_params, 1));

fprintf(fid, '详细结果:\n');
fprintf(fid, '%-10s %-10s %-15s %-15s %-15s %-15s %-15s\n', ...
    'T参数', 'T2参数', '云层事件数', '阈值检测', '水云检测', '梯度检测', '覆盖率(%)');
fprintf(fid, '%s\n', repmat('-', 1, 100));

for i = 1:size(test_params, 1)
    fprintf(fid, '%-10d %-10d %-15d %-15d %-15d %-15d %-15.1f\n', ...
        test_params(i, 1), test_params(i, 2), ...
        results.cloud_detected_times(i), results.threshold_detections(i), ...
        results.water_cloud_detections(i), results.gradient_detections(i), ...
        results.cloud_percentage(i));
end

fprintf(fid, '\n统计摘要:\n');
fprintf(fid, '云层检测事件数 - 最小值: %d, 最大值: %d, 平均值: %.1f\n', ...
    min(results.cloud_detected_times), max(results.cloud_detected_times), mean(results.cloud_detected_times));
fprintf(fid, '云层覆盖率 - 最小值: %.1f%%, 最大值: %.1f%%, 平均值: %.1f%%\n', ...
    min(results.cloud_percentage), max(results.cloud_percentage), mean(results.cloud_percentage));

% 找出最优参数组合
[max_detections, max_idx] = max(results.cloud_detected_times);
[max_coverage, max_cov_idx] = max(results.cloud_percentage);

fprintf(fid, '\n推荐参数:\n');
fprintf(fid, '最多云层检测事件数的参数组合: T=%d, T2=%d (检测事件数: %d)\n', ...
    test_params(max_idx, 1), test_params(max_idx, 2), max_detections);
fprintf(fid, '最高云层覆盖率的参数组合: T=%d, T2=%d (覆盖率: %.1f%%)\n', ...
    test_params(max_cov_idx, 1), test_params(max_cov_idx, 2), max_coverage);

fclose(fid);
fprintf('保存分析报告到: %s\n', report_filename);

%% 显示简要结果
fprintf('\n=== 测试结果摘要 ===\n');
fprintf('最多云层检测事件数: %d (T=%d, T2=%d)\n', ...
    max_detections, test_params(max_idx, 1), test_params(max_idx, 2));
fprintf('最高云层覆盖率: %.1f%% (T=%d, T2=%d)\n', ...
    max_coverage, test_params(max_cov_idx, 1), test_params(max_cov_idx, 2));
fprintf('分析完成！\n');
