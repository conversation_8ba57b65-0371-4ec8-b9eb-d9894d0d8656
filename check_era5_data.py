#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERA5数据检查和验证工具
检查下载的ERA5数据文件的完整性和内容
"""

import os
import glob
import sys
from datetime import datetime

def check_netcdf_file(filepath):
    """
    检查NetCDF文件
    """
    try:
        import netCDF4 as nc
        
        with nc.Dataset(filepath, 'r') as dataset:
            # 获取基本信息
            info = {
                'file_size_mb': os.path.getsize(filepath) / (1024 * 1024),
                'dimensions': dict(dataset.dimensions.items()),
                'variables': list(dataset.variables.keys()),
                'global_attrs': dict(dataset.__dict__.items()) if hasattr(dataset, '__dict__') else {}
            }
            
            # 检查温度变量
            if 't' in dataset.variables:
                temp_var = dataset.variables['t']
                info['temperature_shape'] = temp_var.shape
                info['temperature_units'] = getattr(temp_var, 'units', 'unknown')
                
                # 检查数据范围
                try:
                    temp_data = temp_var[:]
                    info['temperature_min'] = float(temp_data.min())
                    info['temperature_max'] = float(temp_data.max())
                    info['temperature_mean'] = float(temp_data.mean())
                except:
                    info['temperature_stats'] = 'unable to compute'
            
            return True, info
            
    except ImportError:
        return False, "netCDF4 package not installed"
    except Exception as e:
        return False, str(e)

def check_era5_directory():
    """
    检查ERA5数据目录
    """
    era5_dir = r"D:\lidar\supercooledwaterday-hourly\era5"
    
    print("ERA5数据检查工具")
    print("=" * 40)
    print(f"检查目录: {era5_dir}")
    
    if not os.path.exists(era5_dir):
        print(f"错误: 目录不存在: {era5_dir}")
        return False
    
    # 查找所有.nc文件
    nc_pattern = os.path.join(era5_dir, "*.nc")
    nc_files = glob.glob(nc_pattern)
    
    if not nc_files:
        print("未找到任何.nc文件")
        return False
    
    print(f"找到 {len(nc_files)} 个NetCDF文件")
    print()
    
    # 检查每个文件
    valid_files = 0
    total_size = 0
    
    for i, filepath in enumerate(sorted(nc_files), 1):
        filename = os.path.basename(filepath)
        file_size = os.path.getsize(filepath) / (1024 * 1024)  # MB
        total_size += file_size
        
        print(f"[{i:2d}] {filename}")
        print(f"     大小: {file_size:.1f} MB")
        
        # 检查文件内容
        is_valid, info = check_netcdf_file(filepath)
        
        if is_valid:
            print(f"     状态: ✓ 有效")
            print(f"     维度: {info.get('dimensions', {})}")
            if 'temperature_shape' in info:
                print(f"     温度数据形状: {info['temperature_shape']}")
                print(f"     温度单位: {info['temperature_units']}")
                if 'temperature_min' in info:
                    print(f"     温度范围: {info['temperature_min']:.1f} - {info['temperature_max']:.1f} K")
                    print(f"     温度平均: {info['temperature_mean']:.1f} K")
            valid_files += 1
        else:
            print(f"     状态: ✗ 无效 - {info}")
        
        print()
    
    # 总结
    print("检查总结:")
    print(f"  总文件数: {len(nc_files)}")
    print(f"  有效文件: {valid_files}")
    print(f"  无效文件: {len(nc_files) - valid_files}")
    print(f"  总大小: {total_size:.1f} MB")
    
    if valid_files == len(nc_files):
        print("  结果: ✓ 所有文件都有效")
        return True
    else:
        print("  结果: ✗ 存在无效文件")
        return False

def list_missing_dates():
    """
    列出缺失的日期
    """
    # 从激光雷达文件中提取期望的日期
    lidar_dir = r"D:\lidar\supercooledwaterday-hourly"
    expected_dates = set()
    
    depol_pattern = os.path.join(lidar_dir, "Depol_*.csv")
    depol_files = glob.glob(depol_pattern)
    
    for file_path in depol_files:
        filename = os.path.basename(file_path)
        import re
        match = re.search(r'Depol_(\d{8})', filename)
        if match:
            date_str = match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                expected_dates.add(date_str)
            except ValueError:
                continue
    
    # 检查已下载的日期
    era5_dir = r"D:\lidar\supercooledwaterday-hourly\era5"
    downloaded_dates = set()
    
    if os.path.exists(era5_dir):
        nc_pattern = os.path.join(era5_dir, "era5_temperature_*.nc")
        nc_files = glob.glob(nc_pattern)
        
        for file_path in nc_files:
            filename = os.path.basename(file_path)
            import re
            match = re.search(r'era5_temperature_(\d{8})\.nc', filename)
            if match:
                downloaded_dates.add(match.group(1))
    
    # 找出缺失的日期
    missing_dates = expected_dates - downloaded_dates
    
    print("\n日期检查:")
    print(f"  期望日期数: {len(expected_dates)}")
    print(f"  已下载日期数: {len(downloaded_dates)}")
    print(f"  缺失日期数: {len(missing_dates)}")
    
    if missing_dates:
        print("  缺失的日期:")
        for date_str in sorted(missing_dates):
            date_obj = datetime.strptime(date_str, '%Y%m%d')
            print(f"    {date_obj.strftime('%Y-%m-%d')} ({date_str})")
    else:
        print("  ✓ 没有缺失的日期")
    
    return len(missing_dates) == 0

def main():
    """
    主函数
    """
    try:
        # 检查数据文件
        files_ok = check_era5_directory()
        
        # 检查缺失日期
        dates_ok = list_missing_dates()
        
        # 总结
        print("\n" + "=" * 40)
        if files_ok and dates_ok:
            print("✓ 所有检查通过!")
            return True
        else:
            print("✗ 存在问题，请检查上述信息")
            return False
            
    except Exception as e:
        print(f"检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
