#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERA5温度数据下载程序 - 简化版
直接使用固定配置，无需交互
"""

import cdsapi
import os
import glob
import re
from datetime import datetime
import sys

def setup_cds_credentials():
    """
    设置CDS API凭据
    """
    email = "<EMAIL>"
    password = "Zy.76801099"
    
    # 创建.cdsapirc文件
    home_dir = os.path.expanduser("~")
    cdsapirc_path = os.path.join(home_dir, ".cdsapirc")
    
    # CDS API配置内容
    config_content = f"""url: https://cds.climate.copernicus.eu/api/v2
key: {email}:{password}
"""
    
    try:
        with open(cdsapirc_path, 'w') as f:
            f.write(config_content)
        print(f"已设置CDS API凭据")
        return True
    except Exception as e:
        print(f"设置CDS API凭据失败: {e}")
        return False

def extract_dates_from_files():
    """
    从激光雷达文件中提取日期
    """
    lidar_dir = r"D:\lidar\supercooledwaterday-hourly"
    dates = set()
    
    # 查找Depol文件
    depol_pattern = os.path.join(lidar_dir, "Depol_*.csv")
    depol_files = glob.glob(depol_pattern)
    
    print(f"在 {lidar_dir} 中找到 {len(depol_files)} 个Depol文件")
    
    for file_path in depol_files:
        filename = os.path.basename(file_path)
        # 匹配格式: Depol_YYYYMMDD*.csv
        match = re.search(r'Depol_(\d{8})', filename)
        if match:
            date_str = match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                dates.add(date_obj.strftime('%Y-%m-%d'))
            except ValueError:
                continue
    
    return sorted(list(dates))

def download_era5_data():
    """
    下载ERA5数据
    """
    # 固定配置
    output_dir = r"D:\lidar\supercooledwaterday-hourly\era5"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置凭据
    if not setup_cds_credentials():
        return False
    
    # 获取日期列表
    dates = extract_dates_from_files()
    if not dates:
        print("错误: 未找到任何有效日期")
        return False
    
    print(f"将下载 {len(dates)} 天的数据: {dates}")
    
    # 初始化CDS客户端
    try:
        c = cdsapi.Client()
    except Exception as e:
        print(f"CDS客户端初始化失败: {e}")
        return False
    
    # 压力层级和小时
    pressure_levels = [
        '1', '2', '3', '5', '7', '10', '20', '30', '50', '70',
        '100', '125', '150', '175', '200', '225', '250', '300',
        '350', '400', '450', '500', '550', '600', '650', '700',
        '750', '775', '800', '825', '850', '875', '900', '925',
        '950', '975', '1000'
    ]
    hours = [f'{i:02d}:00' for i in range(24)]
    
    # 下载数据
    success_count = 0
    for i, date in enumerate(dates, 1):
        try:
            print(f"\n[{i}/{len(dates)}] 下载日期: {date}")
            
            output_filename = os.path.join(output_dir, f'era5_temperature_{date.replace("-", "")}.nc')
            
            # 检查文件是否已存在
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename) / (1024 * 1024)
                print(f"  文件已存在: {output_filename} ({file_size:.1f} MB)")
                success_count += 1
                continue
            
            # 下载请求
            request_params = {
                'product_type': 'reanalysis',
                'variable': 'temperature',
                'pressure_level': pressure_levels,
                'year': date[:4],
                'month': date[5:7],
                'day': date[8:10],
                'time': hours,
                'format': 'netcdf',
            }
            
            print(f"  正在下载...")
            c.retrieve('reanalysis-era5-pressure-levels', request_params, output_filename)
            
            # 检查下载结果
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename) / (1024 * 1024)
                print(f"  下载成功: {file_size:.1f} MB")
                success_count += 1
            else:
                print(f"  下载失败: 文件未创建")
                
        except Exception as e:
            print(f"  下载失败: {e}")
            continue
    
    print(f"\n下载完成! 成功: {success_count}/{len(dates)}")
    return success_count == len(dates)

def main():
    """
    主函数
    """
    print("ERA5温度数据下载程序 - 简化版")
    print("=" * 40)
    print("配置:")
    print("  账号: <EMAIL>")
    print("  输入: D:\\lidar\\supercooledwaterday-hourly")
    print("  输出: D:\\lidar\\supercooledwaterday-hourly\\era5")
    print("  变量: Temperature")
    print("  层级: 所有37个压力层级")
    print("  时间: 每天24小时")
    
    try:
        success = download_era5_data()
        if success:
            print("\n✓ 所有数据下载完成!")
        else:
            print("\n✗ 部分数据下载失败")
        return success
    except KeyboardInterrupt:
        print("\n\n下载被用户中断")
        return False
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
