#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERA5温度数据下载程序
从Copernicus Climate Data Store下载ERA5逐小时温度数据
作者: 自动生成
日期: 2025-01-18
"""

import cdsapi
import os
import glob
import re
from datetime import datetime, timedelta
import sys

def setup_cds_credentials(email, password):
    """
    设置CDS API凭据
    """
    # 创建.cdsapirc文件
    home_dir = os.path.expanduser("~")
    cdsapirc_path = os.path.join(home_dir, ".cdsapirc")
    
    # CDS API配置内容 - 使用新的API端点
    config_content = f"""url: https://cds.climate.copernicus.eu/api
key: {password}
"""
    
    try:
        with open(cdsapirc_path, 'w') as f:
            f.write(config_content)
        print(f"已创建CDS API配置文件: {cdsapirc_path}")
        return True
    except Exception as e:
        print(f"创建CDS API配置文件失败: {e}")
        return False

def extract_dates_from_matlab_files(lidar_dir):
    """
    从激光雷达文件目录中提取日期
    """
    dates = set()
    
    # 查找Depol文件
    depol_pattern = os.path.join(lidar_dir, "Depol_*.csv")
    depol_files = glob.glob(depol_pattern)
    
    print(f"在 {lidar_dir} 中找到 {len(depol_files)} 个Depol文件")
    
    for file_path in depol_files:
        filename = os.path.basename(file_path)
        # 匹配格式: Depol_YYYYMMDD*.csv
        match = re.search(r'Depol_(\d{8})', filename)
        if match:
            date_str = match.group(1)
            # 转换为YYYY-MM-DD格式
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                dates.add(date_obj.strftime('%Y-%m-%d'))
                print(f"  提取日期: {date_obj.strftime('%Y-%m-%d')} 从文件 {filename}")
            except ValueError:
                print(f"  警告: 无法解析日期 {date_str} 从文件 {filename}")
    
    return sorted(list(dates))

def download_era5_temperature(dates, output_dir, email, password):
    """
    下载ERA5温度数据
    """
    # 设置CDS凭据
    if not setup_cds_credentials(email, password):
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 初始化CDS API客户端
    try:
        c = cdsapi.Client()
        print("CDS API客户端初始化成功")
    except Exception as e:
        print(f"CDS API客户端初始化失败: {e}")
        return False
    
    # 所有压力层级 (hPa)
    pressure_levels = [
        '1', '2', '3', '5', '7', '10', '20', '30', '50', '70',
        '100', '125', '150', '175', '200', '225', '250', '300',
        '350', '400', '450', '500', '550', '600', '650', '700',
        '750', '775', '800', '825', '850', '875', '900', '925',
        '950', '975', '1000'
    ]
    
    # 所有小时 (00:00 到 23:00)
    hours = [f'{i:02d}:00' for i in range(24)]
    
    print(f"将下载 {len(dates)} 天的数据")
    print(f"压力层级: {len(pressure_levels)} 个")
    print(f"每天小时数: {len(hours)} 个")
    
    success_count = 0
    failed_dates = []
    
    for date in dates:
        try:
            print(f"\n正在下载日期: {date}")
            
            # 输出文件名
            output_filename = os.path.join(output_dir, f'era5_temperature_{date.replace("-", "")}.nc')
            
            # 检查文件是否已存在
            if os.path.exists(output_filename):
                print(f"  文件已存在，跳过: {output_filename}")
                success_count += 1
                continue
            
            # 下载请求
            request_params = {
                'product_type': 'reanalysis',
                'variable': 'temperature',
                'pressure_level': pressure_levels,
                'year': date[:4],
                'month': date[5:7],
                'day': date[8:10],
                'time': hours,
                'format': 'netcdf',
            }
            
            print(f"  下载参数:")
            print(f"    年份: {request_params['year']}")
            print(f"    月份: {request_params['month']}")
            print(f"    日期: {request_params['day']}")
            print(f"    压力层级数: {len(pressure_levels)}")
            print(f"    小时数: {len(hours)}")
            print(f"    输出文件: {output_filename}")
            
            # 执行下载
            c.retrieve(
                'reanalysis-era5-pressure-levels',
                request_params,
                output_filename
            )
            
            # 检查下载的文件
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename) / (1024 * 1024)  # MB
                print(f"  下载成功: {output_filename} ({file_size:.1f} MB)")
                success_count += 1
            else:
                print(f"  下载失败: 文件未创建 {output_filename}")
                failed_dates.append(date)
                
        except Exception as e:
            print(f"  下载日期 {date} 时出错: {e}")
            failed_dates.append(date)
            continue
    
    # 下载总结
    print(f"\n下载完成!")
    print(f"成功下载: {success_count}/{len(dates)} 天")
    if failed_dates:
        print(f"失败日期: {failed_dates}")
    
    return success_count == len(dates)

def get_user_config():
    """
    获取用户配置，支持交互式配置
    """
    print("ERA5温度数据下载程序配置")
    print("=" * 50)

    # 默认配置
    default_config = {
        'email': "<EMAIL>",
        'password': "6f1d9d85-2e12-4949-8518-1a733a63b8e7",
        'lidar_dir': r"D:\lidar\supercooledwaterday-hourly",
        'output_dir': r"D:\lidar\supercooledwaterday-hourly\era5"
    }

    print("当前默认配置:")
    print(f"  邮箱: {default_config['email']}")
    print(f"  激光雷达目录: {default_config['lidar_dir']}")
    print(f"  输出目录: {default_config['output_dir']}")

    # 询问是否使用默认配置
    use_default = input("\n是否使用默认配置? (y/n): ")
    if use_default.lower() == 'y':
        return default_config

    # 交互式配置
    config = {}
    config['email'] = input(f"请输入CDS邮箱 (默认: {default_config['email']}): ").strip()
    if not config['email']:
        config['email'] = default_config['email']

    config['password'] = input(f"请输入CDS密码 (默认: {default_config['password']}): ").strip()
    if not config['password']:
        config['password'] = default_config['password']

    config['lidar_dir'] = input(f"请输入激光雷达目录 (默认: {default_config['lidar_dir']}): ").strip()
    if not config['lidar_dir']:
        config['lidar_dir'] = default_config['lidar_dir']

    config['output_dir'] = input(f"请输入输出目录 (默认: {default_config['output_dir']}): ").strip()
    if not config['output_dir']:
        config['output_dir'] = default_config['output_dir']

    return config

def main():
    """
    主函数
    """
    print("ERA5温度数据下载程序")
    print("=" * 50)

    # 获取配置
    config = get_user_config()

    print(f"\n使用配置:")
    print(f"  邮箱: {config['email']}")
    print(f"  激光雷达目录: {config['lidar_dir']}")
    print(f"  输出目录: {config['output_dir']}")

    # 检查激光雷达目录是否存在
    if not os.path.exists(config['lidar_dir']):
        print(f"错误: 激光雷达目录不存在: {config['lidar_dir']}")
        return False

    # 从激光雷达文件中提取日期
    print("\n正在从激光雷达文件中提取日期...")
    dates = extract_dates_from_matlab_files(config['lidar_dir'])

    if not dates:
        print("错误: 未找到任何有效日期")
        return False

    print(f"\n找到 {len(dates)} 个日期:")
    for date in dates:
        print(f"  {date}")

    # 确认下载
    response = input(f"\n是否开始下载这 {len(dates)} 天的ERA5温度数据? (y/n): ")
    if response.lower() != 'y':
        print("下载已取消")
        return False

    # 开始下载
    print("\n开始下载ERA5数据...")
    success = download_era5_temperature(dates, config['output_dir'], config['email'], config['password'])

    if success:
        print("\n所有数据下载完成!")
    else:
        print("\n部分数据下载失败，请检查错误信息")

    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n下载被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        sys.exit(1)
