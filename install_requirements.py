#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装ERA5下载程序所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name):
    """
    安装Python包
    """
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package_name], 
                              capture_output=True, text=True, check=True)
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败:")
        print(f"  错误信息: {e.stderr}")
        return False

def check_package(package_name):
    """
    检查包是否已安装
    """
    try:
        __import__(package_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """
    主函数
    """
    print("ERA5下载程序依赖包安装")
    print("=" * 40)
    
    # 需要安装的包
    required_packages = [
        'cdsapi',      # Copernicus Climate Data Store API
        'netcdf4',     # NetCDF文件处理
        'xarray',      # 科学数据处理
        'numpy',       # 数值计算
        'pandas',      # 数据处理
    ]
    
    print("检查已安装的包...")
    installed_packages = []
    missing_packages = []
    
    for package in required_packages:
        if check_package(package):
            installed_packages.append(package)
        else:
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n所有依赖包都已安装!")
        return True
    
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 确认安装
    response = input(f"\n是否开始安装这些包? (y/n): ")
    if response.lower() != 'y':
        print("安装已取消")
        return False
    
    # 安装缺失的包
    print("\n开始安装...")
    success_count = 0
    
    for package in missing_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成!")
    print(f"成功安装: {success_count}/{len(missing_packages)} 个包")
    
    if success_count == len(missing_packages):
        print("✓ 所有依赖包安装成功!")
        return True
    else:
        print("✗ 部分包安装失败，请手动安装")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        sys.exit(1)
