@echo off
echo ERA5 Temperature Data Download Program
echo ========================================

echo.
echo Please select an option:
echo 1. Full Process (Install + Download + Check)
echo 2. Install Dependencies Only
echo 3. Download Data (Interactive)
echo 4. Download Data (Simple)
echo 5. Check Downloaded Data
echo 6. Test Environment Setup
echo 7. Test CDS API Connection
echo.

set /p choice=Enter your choice (1-7):

if "%choice%"=="1" goto full_process
if "%choice%"=="2" goto install_only
if "%choice%"=="3" goto download_interactive
if "%choice%"=="4" goto download_simple
if "%choice%"=="5" goto check_only
if "%choice%"=="6" goto test_setup
if "%choice%"=="7" goto test_api

echo Invalid choice, exiting...
pause
exit /b 1

:full_process
echo.
echo === Step 1: Installing Dependencies ===
python install_requirements.py
echo.
echo === Step 2: Downloading Data ===
python download_era5_simple.py
echo.
echo === Step 3: Checking Data ===
python check_era5_data.py
goto end

:install_only
echo.
echo === Installing Dependencies ===
python install_requirements.py
goto end

:download_interactive
echo.
echo === Downloading Data (Interactive) ===
python download_era5_temperature.py
goto end

:download_simple
echo.
echo === Downloading Data (Simple) ===
python download_era5_simple.py
goto end

:check_only
echo.
echo === Checking Data ===
python check_era5_data.py
goto end

:test_setup
echo.
echo === Testing Environment Setup ===
python test_era5_setup.py
goto end

:test_api
echo.
echo === Testing CDS API Connection ===
python test_cds_api.py
goto end

:end
echo.
echo Program execution completed.
pause
