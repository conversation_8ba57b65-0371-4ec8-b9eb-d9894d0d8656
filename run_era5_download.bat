@echo off
chcp 65001 >nul
echo ERA5温度数据下载程序
echo ========================

echo.
echo 请选择操作:
echo 1. 完整流程 (安装依赖 + 下载数据 + 检查数据)
echo 2. 仅安装依赖包
echo 3. 仅下载数据 (交互式)
echo 4. 仅下载数据 (简化版)
echo 5. 仅检查数据
echo 6. 测试环境配置
echo.

set /p choice=请输入选择 (1-6):

if "%choice%"=="1" goto full_process
if "%choice%"=="2" goto install_only
if "%choice%"=="3" goto download_interactive
if "%choice%"=="4" goto download_simple
if "%choice%"=="5" goto check_only
if "%choice%"=="6" goto test_setup

echo 无效选择，退出程序
pause
exit /b 1

:full_process
echo.
echo === 步骤1: 安装依赖包 ===
python install_requirements.py
echo.
echo === 步骤2: 下载数据 ===
python download_era5_simple.py
echo.
echo === 步骤3: 检查数据 ===
python check_era5_data.py
goto end

:install_only
echo.
echo === 安装依赖包 ===
python install_requirements.py
goto end

:download_interactive
echo.
echo === 下载数据 (交互式) ===
python download_era5_temperature.py
goto end

:download_simple
echo.
echo === 下载数据 (简化版) ===
python download_era5_simple.py
goto end

:check_only
echo.
echo === 检查数据 ===
python check_era5_data.py
goto end

:test_setup
echo.
echo === 测试环境配置 ===
python test_era5_setup.py
goto end

:end
echo.
echo 程序执行完成
pause
