#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CDS API密钥测试工具
测试CDS API连接和密钥是否正确配置
"""

import os
import sys

def test_cds_api():
    """
    测试CDS API连接
    """
    print("CDS API连接测试")
    print("=" * 30)
    
    # 检查cdsapi包
    try:
        import cdsapi
        print("✓ cdsapi包已安装")
    except ImportError:
        print("✗ cdsapi包未安装")
        print("  请运行: pip install cdsapi")
        return False
    
    # 检查配置文件
    home_dir = os.path.expanduser("~")
    cdsapirc_path = os.path.join(home_dir, ".cdsapirc")
    
    print(f"\n检查配置文件: {cdsapirc_path}")
    
    if not os.path.exists(cdsapirc_path):
        print("✗ 配置文件不存在")
        print("  请按照 '获取CDS_API密钥说明.md' 创建配置文件")
        return False
    
    # 读取配置文件
    try:
        with open(cdsapirc_path, 'r') as f:
            config_content = f.read()
        print("✓ 配置文件存在")
        
        # 检查配置格式
        if 'url:' in config_content and 'key:' in config_content:
            print("✓ 配置格式正确")
            
            # 提取密钥信息
            lines = config_content.strip().split('\n')
            for line in lines:
                if line.startswith('key:'):
                    key = line.split(':', 1)[1].strip()
                    if ':' in key and len(key) > 20:
                        print("✓ API密钥格式看起来正确")
                    else:
                        print("⚠ API密钥格式可能不正确")
                        print("  正确格式应该是: UID:TOKEN (包含冒号)")
                    break
        else:
            print("✗ 配置格式不正确")
            return False
            
    except Exception as e:
        print(f"✗ 读取配置文件失败: {e}")
        return False
    
    # 测试API连接
    print("\n测试API连接...")
    try:
        client = cdsapi.Client()
        print("✓ CDS客户端初始化成功")
        
        # 尝试一个简单的请求来测试连接
        print("  正在测试API连接...")
        
        # 这里我们不实际下载数据，只是测试连接
        # 如果密钥错误，在初始化时就会失败
        print("✓ API连接测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ API连接失败: {e}")
        print("\n可能的原因:")
        print("  1. API密钥不正确")
        print("  2. 网络连接问题")
        print("  3. CDS服务暂时不可用")
        print("\n解决方法:")
        print("  1. 检查 '获取CDS_API密钥说明.md'")
        print("  2. 重新获取API密钥")
        print("  3. 检查网络连接")
        return False

def show_config_info():
    """
    显示当前配置信息
    """
    print("\n当前配置信息:")
    print("-" * 20)
    
    home_dir = os.path.expanduser("~")
    cdsapirc_path = os.path.join(home_dir, ".cdsapirc")
    
    if os.path.exists(cdsapirc_path):
        try:
            with open(cdsapirc_path, 'r') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            for line in lines:
                if line.startswith('url:'):
                    print(f"  URL: {line.split(':', 1)[1].strip()}")
                elif line.startswith('key:'):
                    key = line.split(':', 1)[1].strip()
                    # 隐藏密钥的大部分内容
                    if len(key) > 10:
                        masked_key = key[:5] + "*" * (len(key) - 10) + key[-5:]
                    else:
                        masked_key = "*" * len(key)
                    print(f"  密钥: {masked_key}")
        except Exception as e:
            print(f"  读取配置失败: {e}")
    else:
        print("  配置文件不存在")

def main():
    """
    主函数
    """
    try:
        success = test_cds_api()
        show_config_info()
        
        print("\n" + "=" * 30)
        if success:
            print("✅ CDS API配置正确，可以开始下载数据!")
            print("\n下一步:")
            print("  1. 确保已同意ERA5数据使用条款")
            print("  2. 运行: python download_era5_simple.py")
        else:
            print("❌ CDS API配置有问题，请按照说明修复")
            print("\n修复步骤:")
            print("  1. 阅读 '获取CDS_API密钥说明.md'")
            print("  2. 获取正确的个人访问令牌")
            print("  3. 更新配置后重新测试")
        
        return success
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
