#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERA5下载程序测试脚本
测试环境配置和基本功能
"""

import os
import sys
import glob
import re
from datetime import datetime

def test_python_environment():
    """
    测试Python环境
    """
    print("1. Python环境测试")
    print(f"   Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    
    # 测试必要的内置模块
    required_modules = ['os', 'sys', 'glob', 're', 'datetime', 'subprocess']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✓ {module}")
        except ImportError:
            print(f"   ✗ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"   错误: 缺少必要模块: {missing_modules}")
        return False
    else:
        print("   ✓ Python环境正常")
        return True

def test_optional_packages():
    """
    测试可选包
    """
    print("\n2. 可选包测试")
    optional_packages = {
        'cdsapi': 'CDS API客户端',
        'netCDF4': 'NetCDF文件处理',
        'xarray': '科学数据处理',
        'numpy': '数值计算',
        'pandas': '数据处理'
    }
    
    installed_packages = []
    missing_packages = []
    
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"   ✓ {package} - {description}")
            installed_packages.append(package)
        except ImportError:
            print(f"   ✗ {package} - {description}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"   需要安装: {missing_packages}")
        print("   运行 'python install_requirements.py' 来安装")
    
    return len(installed_packages), len(missing_packages)

def test_directory_structure():
    """
    测试目录结构
    """
    print("\n3. 目录结构测试")
    
    # 测试激光雷达目录
    lidar_dir = r"D:\lidar\supercooledwaterday-hourly"
    print(f"   激光雷达目录: {lidar_dir}")
    
    if os.path.exists(lidar_dir):
        print("   ✓ 目录存在")
        
        # 查找Depol文件
        depol_pattern = os.path.join(lidar_dir, "Depol_*.csv")
        depol_files = glob.glob(depol_pattern)
        print(f"   ✓ 找到 {len(depol_files)} 个Depol文件")
        
        if depol_files:
            # 显示前几个文件
            for i, file_path in enumerate(depol_files[:3]):
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) / (1024 * 1024)
                print(f"     - {filename} ({file_size:.1f} MB)")
            if len(depol_files) > 3:
                print(f"     ... 还有 {len(depol_files) - 3} 个文件")
        
        lidar_ok = len(depol_files) > 0
    else:
        print("   ✗ 目录不存在")
        lidar_ok = False
    
    # 测试输出目录
    output_dir = r"D:\lidar\supercooledwaterday-hourly\era5"
    print(f"   输出目录: {output_dir}")
    
    if os.path.exists(output_dir):
        print("   ✓ 目录存在")
        nc_files = glob.glob(os.path.join(output_dir, "*.nc"))
        print(f"   ✓ 找到 {len(nc_files)} 个NetCDF文件")
        output_ok = True
    else:
        print("   ✗ 目录不存在 (将自动创建)")
        try:
            os.makedirs(output_dir, exist_ok=True)
            print("   ✓ 目录创建成功")
            output_ok = True
        except Exception as e:
            print(f"   ✗ 目录创建失败: {e}")
            output_ok = False
    
    return lidar_ok and output_ok

def test_date_extraction():
    """
    测试日期提取功能
    """
    print("\n4. 日期提取测试")
    
    lidar_dir = r"D:\lidar\supercooledwaterday-hourly"
    if not os.path.exists(lidar_dir):
        print("   ✗ 激光雷达目录不存在，跳过测试")
        return False
    
    dates = set()
    depol_pattern = os.path.join(lidar_dir, "Depol_*.csv")
    depol_files = glob.glob(depol_pattern)
    
    print(f"   处理 {len(depol_files)} 个文件...")
    
    for file_path in depol_files:
        filename = os.path.basename(file_path)
        match = re.search(r'Depol_(\d{8})', filename)
        if match:
            date_str = match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                dates.add(date_obj.strftime('%Y-%m-%d'))
            except ValueError:
                print(f"   警告: 无法解析日期 {date_str}")
    
    dates_list = sorted(list(dates))
    print(f"   ✓ 提取到 {len(dates_list)} 个有效日期")
    
    if dates_list:
        print("   日期范围:")
        print(f"     最早: {dates_list[0]}")
        print(f"     最晚: {dates_list[-1]}")
        
        # 显示前几个日期
        print("   日期列表 (前10个):")
        for date in dates_list[:10]:
            print(f"     - {date}")
        if len(dates_list) > 10:
            print(f"     ... 还有 {len(dates_list) - 10} 个日期")
    
    return len(dates_list) > 0

def test_cds_credentials():
    """
    测试CDS凭据设置
    """
    print("\n5. CDS凭据测试")
    
    home_dir = os.path.expanduser("~")
    cdsapirc_path = os.path.join(home_dir, ".cdsapirc")
    
    print(f"   凭据文件路径: {cdsapirc_path}")
    
    if os.path.exists(cdsapirc_path):
        print("   ✓ 凭据文件存在")
        try:
            with open(cdsapirc_path, 'r') as f:
                content = f.read()
                if '<EMAIL>' in content:
                    print("   ✓ 包含正确的邮箱")
                else:
                    print("   ✗ 邮箱不匹配")
                    return False
        except Exception as e:
            print(f"   ✗ 读取文件失败: {e}")
            return False
    else:
        print("   ✗ 凭据文件不存在")
        # 尝试创建
        try:
            config_content = """url: https://cds.climate.copernicus.eu/api/v2
key: <EMAIL>:Zy.76801099
"""
            with open(cdsapirc_path, 'w') as f:
                f.write(config_content)
            print("   ✓ 凭据文件创建成功")
        except Exception as e:
            print(f"   ✗ 凭据文件创建失败: {e}")
            return False
    
    return True

def main():
    """
    主函数
    """
    print("ERA5下载程序环境测试")
    print("=" * 40)
    
    # 运行所有测试
    tests = [
        ("Python环境", test_python_environment),
        ("目录结构", test_directory_structure),
        ("日期提取", test_date_extraction),
        ("CDS凭据", test_cds_credentials),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ✗ 测试失败: {e}")
            results.append((test_name, False))
    
    # 测试可选包
    installed, missing = test_optional_packages()
    
    # 总结
    print("\n" + "=" * 40)
    print("测试总结:")
    
    passed_tests = sum(1 for _, result in results if result)
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"  可选包: {installed} 已安装, {missing} 缺失")
    
    print(f"\n基础测试: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests and missing == 0:
        print("✓ 所有测试通过，程序可以正常运行!")
        return True
    elif passed_tests == total_tests:
        print("⚠ 基础功能正常，但需要安装依赖包")
        print("  运行 'python install_requirements.py' 来安装依赖包")
        return True
    else:
        print("✗ 存在问题，请检查上述错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
        sys.exit(1)
