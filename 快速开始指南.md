# ERA5数据下载 - 快速开始指南

## 🚀 一键开始

**⚠️ 首次使用必读：**
1. 先阅读 `获取CDS_API密钥说明.md` 获取正确的API密钥
2. 更新程序中的API密钥配置
3. 双击运行 `run_era5_download.bat`
4. 选择 `1` (完整流程)
5. 等待程序自动完成所有步骤

## 📁 文件清单

| 文件名 | 功能 | 推荐使用 |
|--------|------|----------|
| `run_era5_download.bat` | 多功能菜单界面 | ⭐⭐⭐ |
| `download_era5_simple.py` | 简化下载程序 | ⭐⭐⭐ |
| `test_era5_setup.py` | 环境测试工具 | ⭐⭐ |
| `check_era5_data.py` | 数据检查工具 | ⭐⭐ |
| `download_era5_temperature.py` | 完整下载程序 | ⭐ |
| `install_requirements.py` | 依赖安装工具 | ⭐ |

## 🎯 使用场景

### 场景1: 首次使用
```
1. 运行 run_era5_download.bat
2. 选择 6 (测试环境)
3. 选择 1 (完整流程)
```

### 场景2: 只需要下载数据
```
1. 运行 run_era5_download.bat
2. 选择 4 (简化下载)
```

### 场景3: 检查已下载的数据
```
1. 运行 run_era5_download.bat
2. 选择 5 (检查数据)
```

## ⚙️ 程序配置

### 固定配置 (推荐)
- **账号**: <EMAIL>
- **密码**: Zy.76801099
- **输入目录**: `D:\lidar\supercooledwaterday-hourly`
- **输出目录**: `D:\lidar\supercooledwaterday-hourly\era5`

### 下载参数
- **数据源**: ERA5 Reanalysis
- **变量**: Temperature (温度)
- **压力层级**: 37个层级 (1-1000 hPa)
- **时间分辨率**: 逐小时 (24小时/天)
- **文件格式**: NetCDF (.nc)

## 📊 预期结果

### 输入数据
程序会自动扫描以下文件来确定下载日期：
```
D:\lidar\supercooledwaterday-hourly\Depol_YYYYMMDD*.csv
```

### 输出数据
下载的文件将保存为：
```
D:\lidar\supercooledwaterday-hourly\era5\era5_temperature_YYYYMMDD.nc
```

### 文件大小
- 每个文件约 50-100 MB
- 总大小取决于日期数量

## 🔧 故障排除

### 常见问题

**问题1: "找不到Python"**
- 确保已安装Python 3.6+
- 将Python添加到系统PATH

**问题2: "CDS API错误" / "API endpoint not found"**
- 🔑 **最常见原因**：需要使用个人访问令牌而不是登录密码
- 📖 详细解决方法：查看 `获取CDS_API密钥说明.md`
- 🌐 检查网络连接
- ✅ 确认已同意数据使用条款
- ⏰ 等待几分钟后重试

**问题3: "目录不存在"**
- 检查激光雷达数据目录是否正确
- 确保有足够的磁盘空间

**问题4: "依赖包错误"**
- 运行选项2安装依赖包
- 或手动运行: `pip install cdsapi netcdf4 xarray numpy pandas`

### 检查步骤

1. **测试环境**: 选择选项6
2. **检查数据**: 选择选项5
3. **重新安装**: 选择选项2

## 📈 进度监控

程序运行时会显示：
- 找到的日期数量
- 当前下载进度 (X/Y)
- 文件大小信息
- 成功/失败统计

## 🎉 完成标志

看到以下信息表示成功：
```
✓ 所有数据下载完成!
✓ 所有文件都有效
✓ 没有缺失的日期
```

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 运行环境测试 (选项6)
3. 检查 `README_ERA5下载说明.md` 详细文档
4. 访问 CDS官方文档: https://cds.climate.copernicus.eu/api-how-to

---

**提示**: 首次使用建议先运行环境测试，确保所有配置正确后再开始下载。
