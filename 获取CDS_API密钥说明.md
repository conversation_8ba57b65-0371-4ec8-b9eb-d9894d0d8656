# 如何获取CDS API密钥

## ⚠️ 重要提示

程序运行时出现了API端点错误，这是因为需要使用**个人访问令牌(Personal Access Token)**而不是登录密码。

## 🔑 获取API密钥的步骤

### 1. 注册/登录CDS账号
- 访问：https://cds.climate.copernicus.eu/
- 如果没有账号，点击"Register"注册
- 如果已有账号，点击"Login"登录

### 2. 获取个人访问令牌
1. 登录后，访问：https://cds.climate.copernicus.eu/how-to-api
2. 在页面中找到类似这样的代码块：
   ```
   url: https://cds.climate.copernicus.eu/api
   key: <PERSONAL-ACCESS-TOKEN>
   ```
3. 复制`<PERSONAL-ACCESS-TOKEN>`部分（这是一个长字符串）

### 3. 更新程序配置

有两种方法更新API密钥：

#### 方法1: 修改程序文件（推荐）
1. 打开 `download_era5_simple.py` 文件
2. 找到第13行：
   ```python
   password = "Zy.76801099"
   ```
3. 将 `"Zy.76801099"` 替换为您的个人访问令牌：
   ```python
   password = "您的个人访问令牌"
   ```

#### 方法2: 手动创建配置文件
1. 在您的用户目录下创建文件 `.cdsapirc`
   - Windows: `C:\Users\<USER>\.cdsapirc`
   - 文件内容：
     ```
     url: https://cds.climate.copernicus.eu/api
     key: 您的个人访问令牌
     ```

## 🎯 个人访问令牌示例

正确的个人访问令牌通常是这样的格式：
```
12345:abcdef12-3456-7890-abcd-ef1234567890
```

**注意**：
- 令牌包含冒号(:)和连字符(-)
- 长度通常比较长（30-50个字符）
- 不是您的登录密码

## 🔧 修复步骤

1. **获取正确的API密钥**（按上述步骤）
2. **更新程序配置**（选择方法1或方法2）
3. **重新运行程序**：
   ```
   python download_era5_simple.py
   ```

## 📋 同意数据使用条款

在下载数据之前，您还需要：

1. 访问ERA5数据集页面：https://cds.climate.copernicus.eu/datasets/reanalysis-era5-pressure-levels
2. 滚动到页面底部的下载表单
3. 点击"Terms of Use"并同意条款
4. 这一步必须手动完成，否则API请求会被拒绝

## 🆘 如果仍有问题

1. **检查网络连接**：确保能访问 https://cds.climate.copernicus.eu/
2. **验证API密钥**：在CDS网站上重新获取密钥
3. **检查账号状态**：确保账号处于活跃状态
4. **联系支持**：访问 https://confluence.ecmwf.int/category/cd

## 📝 更新后的配置示例

修改后的 `download_era5_simple.py` 应该是这样：

```python
def setup_cds_credentials():
    """
    设置CDS API凭据
    """
    email = "<EMAIL>"
    password = "12345:abcdef12-3456-7890-abcd-ef1234567890"  # 您的个人访问令牌
    
    # ... 其余代码保持不变
```

完成这些步骤后，程序应该能够正常下载ERA5数据。
